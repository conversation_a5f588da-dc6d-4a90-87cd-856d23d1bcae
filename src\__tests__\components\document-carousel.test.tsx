/**
 * @jest-environment jsdom
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { DocumentCarousel } from '@/components/home/<USER>'

// Mock the useTranslation hook
jest.mock('@/hooks/use-translation', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, any> = {
        'carousel.title': 'Document Types We Support',
        'carousel.description': 'From contracts to financial reports, our AI understands and analyzes all types of business documents with precision and intelligence.',
        'carousel.selectType': 'Select Document Type:',
        'carousel.categories.contract': 'Contracts',
        'carousel.categories.financial': 'Financial Documents',
        'carousel.categories.generic': 'Business Documents',
        'carousel.categories.handwritten': 'Handwritten Documents',
        'carousel.categories.insurance': 'Insurance Documents',
        'carousel.categories.manufacturing': 'Manufacturing Specs',
        'carousel.sampleCount.single': 'sample',
        'carousel.sampleCount.multiple': 'samples'
      }
      return translations[key] || key
    },
    isReady: true
  })
}))

// Mock the Button component
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, className, ...props }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      className={className}
      {...props}
    >
      {children}
    </button>
  )
}))

describe('DocumentCarousel', () => {
  beforeEach(() => {
    // Mock timers for auto-rotation
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should render the carousel with title and description', () => {
    render(<DocumentCarousel />)
    
    expect(screen.getByText('Document Types We Support')).toBeInTheDocument()
    expect(screen.getByText(/From contracts to financial reports/)).toBeInTheDocument()
    expect(screen.getByText('Select Document Type:')).toBeInTheDocument()
  })

  it('should render all document categories', () => {
    render(<DocumentCarousel />)
    
    expect(screen.getByText('Contracts')).toBeInTheDocument()
    expect(screen.getByText('Financial Documents')).toBeInTheDocument()
    expect(screen.getByText('Business Documents')).toBeInTheDocument()
    expect(screen.getByText('Handwritten Documents')).toBeInTheDocument()
    expect(screen.getByText('Insurance Documents')).toBeInTheDocument()
    expect(screen.getByText('Manufacturing Specs')).toBeInTheDocument()
  })

  it('should show sample counts for each category', () => {
    render(<DocumentCarousel />)
    
    // Check that sample counts are displayed
    expect(screen.getByText('2 samples')).toBeInTheDocument() // Contracts
    expect(screen.getByText('3 samples')).toBeInTheDocument() // Business Documents
    expect(screen.getByText('1 sample')).toBeInTheDocument() // Manufacturing Specs
  })

  it('should have navigation arrows', () => {
    render(<DocumentCarousel />)
    
    const leftArrow = screen.getByRole('button', { name: /chevron/i })
    expect(leftArrow).toBeInTheDocument()
  })

  it('should allow category selection', async () => {
    render(<DocumentCarousel />)
    
    const financialButton = screen.getByText('Financial Documents')
    fireEvent.click(financialButton)
    
    // Wait for animation to complete
    await waitFor(() => {
      expect(financialButton.closest('button')).toHaveClass('border-orange-400')
    })
  })

  it('should have proper accessibility attributes', () => {
    render(<DocumentCarousel />)
    
    // Check that images have alt text
    const images = screen.getAllByRole('img')
    images.forEach(img => {
      expect(img).toHaveAttribute('alt')
    })
  })

  it('should handle auto-rotation', () => {
    render(<DocumentCarousel />)
    
    // Fast-forward time to trigger auto-rotation
    jest.advanceTimersByTime(4000)
    
    // The component should still be rendered (basic smoke test for auto-rotation)
    expect(screen.getByText('Document Types We Support')).toBeInTheDocument()
  })
})
