import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { DocumentCarousel } from '@/components/home/<USER>'

// Mock the translation hook
jest.mock('@/hooks/use-translation', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, any> = {
        'carousel.title': 'Your Everyday AI Document Assistant',
        'carousel.description': 'From busy workdays to important projects, our AI companion makes document analysis effortless.',
        'carousel.selectType': 'Document Examples:',
        'carousel.categories.contract': 'Contract Documents',
        'carousel.categories.financial': 'Financial Documents',
        'carousel.categories.generic': 'Business Documents',
        'carousel.categories.handwritten': 'Handwritten Documents',
        'carousel.categories.insurance': 'Insurance Documents',
        'carousel.categories.manufacturing': 'Manufacturing Specifications',
        'carousel.sampleCount.single': 'sample',
        'carousel.sampleCount.multiple': 'samples'
      }
      return translations[key] || key
    }
  })
}))

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, ...props }: any) {
    return <img src={src} alt={alt} {...props} />
  }
})

describe('DocumentCarousel', () => {
  it('renders the carousel with title and description', () => {
    render(<DocumentCarousel />)
    
    expect(screen.getByText('Your Everyday AI Document Assistant')).toBeInTheDocument()
    expect(screen.getByText('From busy workdays to important projects, our AI companion makes document analysis effortless.')).toBeInTheDocument()
  })

  it('renders all document categories', () => {
    render(<DocumentCarousel />)
    
    expect(screen.getByText('Contract Documents')).toBeInTheDocument()
    expect(screen.getByText('Financial Documents')).toBeInTheDocument()
    expect(screen.getByText('Business Documents')).toBeInTheDocument()
    expect(screen.getByText('Handwritten Documents')).toBeInTheDocument()
    expect(screen.getByText('Insurance Documents')).toBeInTheDocument()
    expect(screen.getByText('Manufacturing Specifications')).toBeInTheDocument()
  })

  it('shows sample count for each category', () => {
    render(<DocumentCarousel />)
    
    // Contract category should show 2 samples
    expect(screen.getByText('(2 samples)')).toBeInTheDocument()
    // Manufacturing category should show 1 sample
    expect(screen.getByText('(1 sample)')).toBeInTheDocument()
  })

  it('changes category when clicked', () => {
    render(<DocumentCarousel />)
    
    const financialButton = screen.getByText('Financial Documents')
    fireEvent.click(financialButton)
    
    // The button should have active styling
    expect(financialButton.closest('button')).toHaveClass('bg-purple-100', 'text-purple-700')
  })

  it('renders carousel content', () => {
    render(<DocumentCarousel />)
    
    // Should render the carousel structure
    expect(document.querySelector('[role="region"][aria-roledescription="carousel"]')).toBeInTheDocument()
  })
})
