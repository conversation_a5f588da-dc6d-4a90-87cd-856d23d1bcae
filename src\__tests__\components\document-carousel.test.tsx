/**
 * @jest-environment jsdom
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { DocumentCarousel } from '@/components/home/<USER>'

// Mock the useTranslation hook
jest.mock('@/hooks/use-translation', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, any> = {
        'carousel.title': 'Your Everyday AI Document Assistant',
        'carousel.description': 'From busy workdays to important projects, our AI companion makes document analysis feel easy, providing summaries and highlights in a personal, approachable way.',
        'carousel.selectType': 'Document Examples:',
        'carousel.categories.contract': 'Contracts',
        'carousel.categories.financial': 'Financial Documents',
        'carousel.categories.generic': 'Business Documents',
        'carousel.categories.handwritten': 'Handwritten Documents',
        'carousel.categories.insurance': 'Insurance Documents',
        'carousel.categories.manufacturing': 'Manufacturing Specs',
        'carousel.sampleCount.single': 'sample',
        'carousel.sampleCount.multiple': 'samples'
      }
      return translations[key] || key
    },
    isReady: true
  })
}))

// Mock the Button component
jest.mock('@/components/ui/button', () => ({
  Button: ({ children, onClick, disabled, className, ...props }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      className={className}
      {...props}
    >
      {children}
    </button>
  )
}))

describe('DocumentCarousel', () => {
  beforeEach(() => {
    // Mock timers for auto-rotation
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should render the carousel with title and description', () => {
    render(<DocumentCarousel />)

    expect(screen.getByText('Your Everyday AI Document Assistant')).toBeInTheDocument()
    expect(screen.getByText(/From busy workdays to important projects/)).toBeInTheDocument()
    expect(screen.getByText('Document Examples:')).toBeInTheDocument()
  })

  it('should render document catalog with individual samples', () => {
    render(<DocumentCarousel />)

    // Check that document categories are displayed in the catalog
    expect(screen.getByText('Contracts')).toBeInTheDocument()
    expect(screen.getByText('Financial Documents')).toBeInTheDocument()
    expect(screen.getByText('Business Documents')).toBeInTheDocument()
    expect(screen.getByText('Handwritten Documents')).toBeInTheDocument()
    expect(screen.getByText('Insurance Documents')).toBeInTheDocument()
    expect(screen.getByText('Manufacturing Specs')).toBeInTheDocument()
  })

  it('should show individual document samples in catalog style', () => {
    render(<DocumentCarousel />)

    // Check that individual samples are displayed
    expect(screen.getByText('Sample 1')).toBeInTheDocument()
    expect(screen.getByText('Sample 2')).toBeInTheDocument()
    // Should have multiple sample cards
    const sampleElements = screen.getAllByText(/Sample \d+/)
    expect(sampleElements.length).toBeGreaterThan(5) // Should have multiple samples
  })

  it('should have navigation arrows', () => {
    render(<DocumentCarousel />)
    
    const leftArrow = screen.getByRole('button', { name: /chevron/i })
    expect(leftArrow).toBeInTheDocument()
  })

  it('should allow document sample selection', async () => {
    render(<DocumentCarousel />)

    const sampleButtons = screen.getAllByText(/Sample \d+/)
    if (sampleButtons.length > 0) {
      fireEvent.click(sampleButtons[0])

      // Wait for animation to complete
      await waitFor(() => {
        expect(sampleButtons[0].closest('button')).toHaveClass('border-blue-400')
      })
    }
  })

  it('should have proper accessibility attributes', () => {
    render(<DocumentCarousel />)
    
    // Check that images have alt text
    const images = screen.getAllByRole('img')
    images.forEach(img => {
      expect(img).toHaveAttribute('alt')
    })
  })

  it('should handle auto-rotation', () => {
    render(<DocumentCarousel />)
    
    // Fast-forward time to trigger auto-rotation
    jest.advanceTimersByTime(4000)
    
    // The component should still be rendered (basic smoke test for auto-rotation)
    expect(screen.getByText('Your Everyday AI Document Assistant')).toBeInTheDocument()
  })
})
