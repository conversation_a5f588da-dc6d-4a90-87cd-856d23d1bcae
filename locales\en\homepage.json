{"banner": {"limitedOffer": "🎉 Limited Offer: New sign-up users receive 10 free credits for trial!"}, "hero": {"title": {"line1": "Exhausted with reading hundreds", "line2": "of pages of"}, "documentTypes": ["financial reports", "legal documents", "insurance policies", "request for proposals", "research papers", "compliance documents"], "subtitle": "DocuChampAI is the AI-powered document analysis platform for professionals who value efficiency over manual processes.", "description": "Transform your document workflow with advanced AI analysis. Extract insights from PDFs, analyze charts & tables, and automate reports - wherever you are.", "buttons": {"getStartedFree": "Get Started for Free", "viewPricing": "View Subscription Plans"}}, "videoSection": {"title": "See DocuChampAI in Action", "description": "Watch how our AI-powered document analysis transforms complex documents into actionable insights in minutes.", "loadingText": "Loading video...", "videoDescription": "This demonstration shows the complete workflow from document upload to AI analysis, highlighting key features like multimodal AI extraction, customizable templates, and automated report generation.", "muteButton": "Toggle sound"}, "valueProposition": {"heavyLifting": "Let AI handle the heavy lifting while you focus on what matters most.", "startSaving": "Start Saving Time Today"}, "trialSection": {"title": "Try DocuChampAI - Analyze your Document Instantly", "description": "Experience the power of AI document analysis without signing up. Upload a document and get instant insights, summaries, and recommendations."}, "whySection": {"title": "Why DocuChampAI?", "description": "See how our AI-powered document analysis platform outperforms traditional automation tools", "comparisonTable": {"headers": {"feature": "Feature", "others": "Others", "docuchamp": "DocuChampAI"}, "rows": {"dataUnderstanding": {"feature": "Data Understanding", "others": "Text only, many data loss", "docuchamp": "DocuChampAI reads everything, image, chart, table, text or even handwriting"}, "documentSize": {"feature": "Document Size", "others": "Small documents, <50 pages", "docuchamp": "Support files up to 300 pages, enabling deep insights from full-length materials"}, "useCase": {"feature": "Use Case", "others": "Chatbot interface, not practical for daily work, too many manual copy-and-paste", "docuchamp": "Designed for efficiency, output to .docx, .pdf directly without copy-and-paste"}}}}, "aiFeatures": {"title": "AI That \"Sees\" Pictures, Charts, and Anything a Human Can", "description": "DocuChampAI doesn't just scan, it truly seeing pictures and patterns like a person would. Whether it's numbers, charts, tables or photos, our AI gives every element within your documents the attention.", "features": {"textAnalysis": {"title": "Text Analysis", "description": "Extract and analyze textual content with context understanding"}, "chartRecognition": {"title": "Chart Recognition", "description": "Identify and interpret charts, graphs, and visual data representations"}, "tableExtraction": {"title": "Table Extraction", "description": "Extract structured data from tables with high accuracy"}, "imageAnalysis": {"title": "Image Analysis", "description": "Analyze images, diagrams, and visual elements within documents"}}, "supportInfo": {"pages": "📄 Supports up to 300 pages", "formats": "📁 PDF, DOCX, PPTX formats"}, "featureList": {"chartAnalysis": {"title": "Chart Analysis", "description": "Understand financial charts, graphs, and visual data representations"}, "tableExtraction": {"title": "Table Extraction", "description": "Extract structured data from tables with high accuracy"}, "imageRecognition": {"title": "Image Recognition", "description": "Understand diagrams, photos, and visual elements within documents"}, "textAnalysis": {"title": "Text Analysis", "description": "Extract and analyze textual content with context understanding"}}}, "carousel": {"title": "Your Everyday AI Document Assistant", "description": "From busy workdays to important projects, our AI companion makes document analysis feel easy, providing summaries and highlights in a personal, approachable way.", "selectType": "Document Examples:", "categories": {"contract": "Contracts", "financial": "Financial Documents", "generic": "Business Documents", "handwritten": "Handwritten Documents", "insurance": "Insurance Documents", "manufacturing": "Manufacturing Specs"}, "sampleCount": {"single": "sample", "multiple": "samples"}}, "cta": {"title": "Ready to Transform Your Document Analysis?", "description": "Join thousands of users who are already using AI to analyze their documents smarter.", "button": "Get Started Today"}}