'use client'

import React, { useState, useEffect } from 'react'
import { useTranslation } from '@/hooks/use-translation'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi
} from '@/components/ui/carousel'

// Document categories and subcategories structure
interface DocumentSubcategory {
  name: string;
  path: string;
}

interface DocumentCategory {
  id: string;
  subcategories: DocumentSubcategory[];
}

// Define document categories and their subcategories
const DOCUMENT_CATEGORIES: DocumentCategory[] = [
  {
    id: 'contract',
    subcategories: [
      { name: 'NDA', path: '/home-page-carousel/contract-NDA.png' },
      { name: 'Employee', path: '/home-page-carousel/contract-employee.webp' }
    ]
  },
  {
    id: 'financial',
    subcategories: [
      { name: 'Financial Report', path: '/home-page-carousel/financial-report.png' },
      { name: 'Financial Statement', path: '/home-page-carousel/financial-statement.png' }
    ]
  },
  {
    id: 'generic',
    subcategories: [
      { name: 'Invoice', path: '/home-page-carousel/generic-invoice.png' },
      { name: 'Purchase Order', path: '/home-page-carousel/generic-PO.webp' },
      { name: 'RFP', path: '/home-page-carousel/generic-RFP.jpg' }
    ]
  },
  {
    id: 'handwritten',
    subcategories: [
      { name: 'Flowchart', path: '/home-page-carousel/handwriten-flowchart.webp' },
      { name: 'Form', path: '/home-page-carousel/handwriten-form.png' }
    ]
  },
  {
    id: 'insurance',
    subcategories: [
      { name: 'Claim Form', path: '/home-page-carousel/insurance-claimform.png' },
      { name: 'Policy', path: '/home-page-carousel/insurance.pnb.png' }
    ]
  },
  {
    id: 'manufacturing',
    subcategories: [
      { name: 'Specification', path: '/home-page-carousel/manufacturing-spec.png' }
    ]
  }
];

export function DocumentCarousel() {
  const { t } = useTranslation('homepage')
  const [selectedCategory, setSelectedCategory] = useState<string>('financial')
  const [documents, setDocuments] = useState<string[]>([])
  const [api, setApi] = useState<CarouselApi>()
  const [current, setCurrent] = useState(0)

  // Update documents when category changes
  useEffect(() => {
    const category = DOCUMENT_CATEGORIES.find(cat => cat.id === selectedCategory);
    if (category) {
      setDocuments(category.subcategories.map(sub => sub.path));
    }
  }, [selectedCategory])

  // Handle carousel API
  useEffect(() => {
    if (!api) {
      return
    }

    setCurrent(api.selectedScrollSnap())

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap())
    })
  }, [api])

  // Handle category selection
  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category)
  }

  return (
    <section className="py-16 bg-gradient-to-br from-purple-50 to-pink-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold mb-4">{t('carousel.title')}</h2>
          <p className="text-gray-600 max-w-3xl mx-auto">
            {t('carousel.description')}
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-8">
          {/* Category Selection - New Layout */}
          <div className="w-full md:w-1/4">
            <h3 className="text-lg font-medium mb-4">{t('carousel.selectType')}</h3>
            <div className="space-y-6">
              {DOCUMENT_CATEGORIES.map((category) => (
                <div key={category.id} className="mb-6">
                  <h4 className={`font-medium capitalize mb-2 ${
                    selectedCategory === category.id ? 'text-purple-700' : 'text-gray-700'
                  }`}>
                    {t(`carousel.categories.${category.id}`)}
                  </h4>
                  <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                    {category.subcategories.map((subcat) => (
                      <button
                        key={subcat.path}
                        onClick={() => {
                          handleCategorySelect(category.id);
                          // Find the index of this subcategory and scroll to it
                          const index = category.subcategories.findIndex(s => s.path === subcat.path);
                          if (index >= 0 && api) {
                            api.scrollTo(index);
                          }
                        }}
                        className={`text-left text-sm ${
                          selectedCategory === category.id ? 'text-purple-600 hover:text-purple-800' : 'text-gray-600 hover:text-gray-800'
                        }`}
                      >
                        {subcat.name}
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Carousel - Side-by-side layout */}
          <div className="w-full md:w-3/4">
            <div className="relative">
              <Carousel
                setApi={setApi}
                className="w-full"
                opts={{
                  align: 'center',
                  loop: true,
                  skipSnaps: false,
                  containScroll: 'trimSnaps'
                }}
              >
                <CarouselContent>
                  {documents.map((doc, index) => (
                    <CarouselItem key={doc} className="basis-full transition-all duration-500 overflow-visible">
                      <div className="relative h-[500px] mx-auto perspective">
                        {/* We'll render all documents but position them differently */}
                        {documents.map((innerDoc, innerIndex) => {
                          // Calculate the relative position from current
                          const position = (innerIndex - current + documents.length) % documents.length;
                          // Normalize to -1, 0, 1 for left, center, right
                          const normalizedPosition = position > documents.length / 2
                            ? position - documents.length
                            : position;

                          // Determine styling based on position
                          let styling = '';
                          let transform = '';
                          let zIndex = 0;

                          if (normalizedPosition === 0) {
                            // Center (current) item
                            styling = 'opacity-100 bg-white';
                            transform = 'scale(1) translateX(0)';
                            zIndex = 30;
                          } else if (normalizedPosition === -1 || normalizedPosition === documents.length - 1) {
                            // Left item
                            styling = 'opacity-60 bg-white/90';
                            transform = 'scale(0.85) translateX(-70%)';
                            zIndex = 20;
                          } else if (normalizedPosition === 1) {
                            // Right item
                            styling = 'opacity-60 bg-white/90';
                            transform = 'scale(0.85) translateX(70%)';
                            zIndex = 20;
                          } else if (normalizedPosition === -2 || normalizedPosition === documents.length - 2) {
                            // Far left item
                            styling = 'opacity-30 bg-white/80';
                            transform = 'scale(0.7) translateX(-120%)';
                            zIndex = 10;
                          } else if (normalizedPosition === 2) {
                            // Far right item
                            styling = 'opacity-30 bg-white/80';
                            transform = 'scale(0.7) translateX(120%)';
                            zIndex = 10;
                          } else {
                            // Hidden items
                            styling = 'opacity-0';
                            transform = 'scale(0.6)';
                            zIndex = 0;
                          }

                          return (
                            <div
                              key={innerDoc}
                              className={`absolute top-0 left-0 right-0 bottom-0 rounded-lg shadow-xl overflow-hidden border border-gray-200 transition-all duration-500 ${styling}`}
                              style={{
                                transform,
                                zIndex
                              }}
                            >
                              <div className="w-full h-full flex items-center justify-center bg-gradient-to-b from-gray-50 to-white p-4">
                                <img
                                  src={innerDoc}
                                  alt={`Document ${innerIndex + 1}`}
                                  className="max-w-full max-h-full object-contain"
                                />
                              </div>

                              {/* Card title - only show on center card */}
                              {normalizedPosition === 0 && (
                                <div className="absolute top-4 left-0 right-0 text-center">
                                  <h3 className="text-gray-700 font-medium text-lg">
                                    {DOCUMENT_CATEGORIES.find(cat => cat.id === selectedCategory)?.subcategories[innerIndex]?.name || `Card ${innerIndex + 1}`}
                                  </h3>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>

                <CarouselPrevious className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-white border-gray-300 z-40 rounded-full" />
                <CarouselNext className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white hover:bg-white border-gray-300 z-40 rounded-full" />
              </Carousel>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
