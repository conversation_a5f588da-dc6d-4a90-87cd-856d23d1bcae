'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { useTranslation } from '@/hooks/use-translation'
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi
} from '@/components/ui/carousel'

// Document categories based on file prefixes
const DOCUMENT_CATEGORIES = [
  'contract',
  'financial',
  'generic',
  'handwritten',
  'insurance',
  'manufacturing'
]

// Get document images for a specific category
const getDocumentsByCategory = (category: string): string[] => {
  // This would ideally be dynamically loaded, but for simplicity we'll hardcode based on the files we saw
  switch (category) {
    case 'contract':
      return ['/home-page-carousel/contract-NDA.png', '/home-page-carousel/contract-employee.webp']
    case 'financial':
      return ['/home-page-carousel/financial-report.png', '/home-page-carousel/financial-statement.png']
    case 'generic':
      return [
        '/home-page-carousel/generic-PO.webp',
        '/home-page-carousel/generic-RFP.jpg',
        '/home-page-carousel/generic-invoice.png'
      ]
    case 'handwritten':
      return ['/home-page-carousel/handwriten-flowchart.webp', '/home-page-carousel/handwriten-form.png']
    case 'insurance':
      return ['/home-page-carousel/insurance-claimform.png', '/home-page-carousel/insurance.pnb.png']
    case 'manufacturing':
      return ['/home-page-carousel/manufacturing-spec.png']
    default:
      return []
  }
}

export function DocumentCarousel() {
  const { t } = useTranslation('homepage')
  const [selectedCategory, setSelectedCategory] = useState<string>('contract')
  const [documents, setDocuments] = useState<string[]>([])
  const [api, setApi] = useState<CarouselApi>()
  const [current, setCurrent] = useState(0)

  // Update documents when category changes
  useEffect(() => {
    const docs = getDocumentsByCategory(selectedCategory)
    setDocuments(docs)
  }, [selectedCategory])

  // Handle carousel API
  useEffect(() => {
    if (!api) {
      return
    }

    setCurrent(api.selectedScrollSnap())

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap())
    })
  }, [api])

  // Handle category selection
  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category)
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold mb-4">{t('carousel.title')}</h2>
          <p className="text-gray-600 max-w-3xl mx-auto">
            {t('carousel.description')}
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-8">
          {/* Category Selection */}
          <div className="w-full md:w-1/4">
            <h3 className="text-lg font-medium mb-4">{t('carousel.selectType')}</h3>
            <div className="flex flex-col space-y-2">
              {DOCUMENT_CATEGORIES.map((category) => (
                <button
                  key={category}
                  onClick={() => handleCategorySelect(category)}
                  className={`text-left px-4 py-2 rounded-lg transition-colors ${
                    selectedCategory === category
                      ? 'bg-purple-100 text-purple-700 font-medium'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  {t(`carousel.categories.${category}`)}
                  <span className="text-sm text-gray-500 ml-2">
                    ({getDocumentsByCategory(category).length}{' '}
                    {getDocumentsByCategory(category).length === 1
                      ? t('carousel.sampleCount.single')
                      : t('carousel.sampleCount.multiple')}
                    )
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Carousel */}
          <div className="w-full md:w-3/4">
            <div className="relative px-16">
              <Carousel setApi={setApi} className="w-full">
                <CarouselContent>
                  {documents.map((doc, index) => (
                    <CarouselItem key={doc}>
                      <div className="relative pb-20">
                        {/* Main Document - A4 Proportions with space around */}
                        <div className="relative aspect-[1/1.414] max-w-[400px] mx-auto bg-white rounded-lg shadow-2xl overflow-hidden border border-gray-200 p-4">
                          <div className="w-full h-full bg-gray-50 rounded flex items-center justify-center">
                            <img
                              src={doc}
                              alt={`${t(`carousel.categories.${selectedCategory}`)} ${index + 1}`}
                              className="max-w-full max-h-full object-contain"
                            />
                          </div>
                        </div>

                        {/* Background Documents - Layered underneath */}
                        {documents.length > 1 && (
                          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-full h-16 flex justify-center items-end">
                            {documents.map((bgDoc, bgIndex) => {
                              // Skip the current document
                              if (bgIndex === current) return null

                              // Calculate position for layered effect
                              const isLeft = bgIndex < current
                              const distance = Math.abs(bgIndex - current)
                              const offset = isLeft ? -20 - (distance * 15) : 20 + (distance * 15)
                              const scale = 0.8 - (distance * 0.1)
                              const zIndex = 10 - distance

                              return (
                                <div
                                  key={`bg-${bgDoc}`}
                                  className="absolute bg-white rounded shadow-lg overflow-hidden border border-gray-200 transition-all duration-500"
                                  style={{
                                    width: '120px',
                                    height: '80px',
                                    left: `calc(50% + ${offset}px)`,
                                    transform: `translateX(-50%) scale(${scale})`,
                                    zIndex: zIndex,
                                    opacity: 0.6
                                  }}
                                >
                                  <img
                                    src={bgDoc}
                                    alt=""
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                              )
                            })}
                          </div>
                        )}
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>

                {documents.length > 1 && (
                  <>
                    <CarouselPrevious className="absolute left-0 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white border-gray-300" />
                    <CarouselNext className="absolute right-0 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white border-gray-300" />
                  </>
                )}
              </Carousel>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
