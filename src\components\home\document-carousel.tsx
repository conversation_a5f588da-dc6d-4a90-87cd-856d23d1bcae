'use client'

import React, { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useTranslation } from '@/hooks/use-translation'

interface DocumentCategory {
  id: string
  name: string
  displayName: string
  images: string[]
}

const getDocumentCategories = (t: any): DocumentCategory[] => [
  {
    id: 'contract',
    name: 'contract',
    displayName: t('carousel.categories.contract'),
    images: [
      '/home-page-carousel/contract-NDA.png',
      '/home-page-carousel/contract-employee.webp'
    ]
  },
  {
    id: 'financial',
    name: 'financial',
    displayName: t('carousel.categories.financial'),
    images: [
      '/home-page-carousel/financial-report.png',
      '/home-page-carousel/financial-statement.png'
    ]
  },
  {
    id: 'generic',
    name: 'generic',
    displayName: t('carousel.categories.generic'),
    images: [
      '/home-page-carousel/generic-PO.webp',
      '/home-page-carousel/generic-RFP.jpg',
      '/home-page-carousel/generic-invoice.png'
    ]
  },
  {
    id: 'handwritten',
    name: 'handwriten',
    displayName: t('carousel.categories.handwritten'),
    images: [
      '/home-page-carousel/handwriten-flowchart.webp',
      '/home-page-carousel/handwriten-form.png'
    ]
  },
  {
    id: 'insurance',
    name: 'insurance',
    displayName: t('carousel.categories.insurance'),
    images: [
      '/home-page-carousel/insurance-claimform.png',
      '/home-page-carousel/insurance.pnb.png'
    ]
  },
  {
    id: 'manufacturing',
    name: 'manufacturing',
    displayName: t('carousel.categories.manufacturing'),
    images: [
      '/home-page-carousel/manufacturing-spec.png'
    ]
  }
]

export function DocumentCarousel() {
  const { t } = useTranslation('homepage')
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [selectedCategory, setSelectedCategory] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  // Get document categories with translations
  const documentCategories = getDocumentCategories(t)

  // Get all images in order
  const allImages = documentCategories.flatMap(category => category.images)
  
  // Auto-rotate carousel
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isAnimating) {
        nextImage()
      }
    }, 4000)

    return () => clearInterval(interval)
  }, [currentImageIndex, isAnimating])

  const nextImage = () => {
    if (isAnimating) return
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentImageIndex((prev) => (prev + 1) % allImages.length)
      setIsAnimating(false)
    }, 300)
  }

  const prevImage = () => {
    if (isAnimating) return
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentImageIndex((prev) => (prev - 1 + allImages.length) % allImages.length)
      setIsAnimating(false)
    }, 300)
  }

  const goToCategory = (categoryIndex: number) => {
    if (isAnimating || categoryIndex === selectedCategory) return
    
    setIsAnimating(true)
    setSelectedCategory(categoryIndex)
    
    // Calculate the first image index for this category
    let imageIndex = 0
    for (let i = 0; i < categoryIndex; i++) {
      imageIndex += documentCategories[i].images.length
    }
    
    setTimeout(() => {
      setCurrentImageIndex(imageIndex)
      setIsAnimating(false)
    }, 300)
  }

  // Update selected category based on current image
  useEffect(() => {
    let imageCount = 0
    for (let i = 0; i < documentCategories.length; i++) {
      imageCount += documentCategories[i].images.length
      if (currentImageIndex < imageCount) {
        setSelectedCategory(i)
        break
      }
    }
  }, [currentImageIndex])

  return (
    <section className="py-16 bg-gradient-to-br from-orange-50 via-yellow-50 to-amber-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            {t('carousel.title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {t('carousel.description')}
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
          {/* Left Side - Document Categories */}
          <div className="w-full lg:w-1/3 order-2 lg:order-1">
            <h3 className="text-xl font-semibold text-gray-800 mb-6 text-center lg:text-left">{t('carousel.selectType')}</h3>
            <div className="grid grid-cols-2 lg:grid-cols-1 gap-3">
              {documentCategories.map((category, index) => (
                <button
                  key={category.id}
                  onClick={() => goToCategory(index)}
                  disabled={isAnimating}
                  className={`w-full text-left p-3 lg:p-4 rounded-lg border-2 transition-all duration-300 ${
                    selectedCategory === index
                      ? 'border-orange-400 bg-orange-100 text-orange-800 shadow-md transform scale-105 lg:scale-100'
                      : 'border-gray-200 bg-white text-gray-700 hover:border-orange-200 hover:bg-orange-50'
                  } ${isAnimating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <div className="font-medium text-sm lg:text-base">{category.displayName}</div>
                  <div className="text-xs lg:text-sm opacity-75 mt-1">
                    {category.images.length} {category.images.length > 1 ? t('carousel.sampleCount.multiple') : t('carousel.sampleCount.single')}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Right Side - 3D Carousel */}
          <div className="w-full lg:w-2/3 order-1 lg:order-2">
            <div className="relative">
              {/* Carousel Container */}
              <div className="relative h-64 sm:h-80 lg:h-96 perspective-1000">
                <div className="relative w-full h-full flex items-center justify-center">
                  {/* Main Image */}
                  <div className={`relative w-56 h-56 sm:w-72 sm:h-72 lg:w-80 lg:h-80 transition-all duration-500 ease-in-out transform ${
                    isAnimating ? 'scale-95 opacity-70 rotate-y-12' : 'scale-100 opacity-100 rotate-y-0'
                  }`}>
                    <div className="w-full h-full bg-white rounded-xl lg:rounded-2xl shadow-2xl border border-gray-200 overflow-hidden transform-gpu">
                      <img
                        src={allImages[currentImageIndex]}
                        alt={`Document sample ${currentImageIndex + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>

                  {/* Side Images for 3D Effect - Hidden on mobile */}
                  <div className="hidden sm:block absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-4 lg:-translate-x-8 rotate-y-45 scale-75 opacity-30">
                    <div className="w-48 h-48 lg:w-64 lg:h-64 bg-white rounded-lg lg:rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                      <img
                        src={allImages[(currentImageIndex - 1 + allImages.length) % allImages.length]}
                        alt="Previous document"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>

                  <div className="hidden sm:block absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-4 lg:translate-x-8 -rotate-y-45 scale-75 opacity-30">
                    <div className="w-48 h-48 lg:w-64 lg:h-64 bg-white rounded-lg lg:rounded-xl shadow-lg border border-gray-200 overflow-hidden">
                      <img
                        src={allImages[(currentImageIndex + 1) % allImages.length]}
                        alt="Next document"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Navigation Arrows */}
              <Button
                onClick={prevImage}
                disabled={isAnimating}
                variant="outline"
                size="icon"
                className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white border-gray-300 shadow-lg z-10 w-8 h-8 sm:w-10 sm:h-10"
              >
                <ChevronLeft className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>

              <Button
                onClick={nextImage}
                disabled={isAnimating}
                variant="outline"
                size="icon"
                className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white border-gray-300 shadow-lg z-10 w-8 h-8 sm:w-10 sm:h-10"
              >
                <ChevronRight className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>

              {/* Dots Indicator */}
              <div className="flex justify-center mt-6 space-x-2">
                {allImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      if (!isAnimating) {
                        setIsAnimating(true)
                        setTimeout(() => {
                          setCurrentImageIndex(index)
                          setIsAnimating(false)
                        }, 300)
                      }
                    }}
                    disabled={isAnimating}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      currentImageIndex === index
                        ? 'bg-orange-500 scale-125'
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .perspective-1000 {
          perspective: 1000px;
        }
        .rotate-y-45 {
          transform: rotateY(45deg);
        }
        .-rotate-y-45 {
          transform: rotateY(-45deg);
        }
        .rotate-y-12 {
          transform: rotateY(12deg);
        }
        .rotate-y-0 {
          transform: rotateY(0deg);
        }
        .transform-gpu {
          transform-style: preserve-3d;
        }
      `}</style>
    </section>
  )
}
