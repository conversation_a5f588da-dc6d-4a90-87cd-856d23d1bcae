'use client'

import React, { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useTranslation } from '@/hooks/use-translation'
import './document-carousel.css' // Import custom CSS for 3D effects

interface DocumentCategory {
  id: string
  name: string
  displayName: string
  images: string[]
}

const getDocumentCategories = (t: any): DocumentCategory[] => [
  {
    id: 'contract',
    name: 'contract',
    displayName: t('carousel.categories.contract'),
    images: [
      '/home-page-carousel/contract-NDA.png',
      '/home-page-carousel/contract-employee.webp'
    ]
  },
  {
    id: 'financial',
    name: 'financial',
    displayName: t('carousel.categories.financial'),
    images: [
      '/home-page-carousel/financial-report.png',
      '/home-page-carousel/financial-statement.png'
    ]
  },
  {
    id: 'generic',
    name: 'generic',
    displayName: t('carousel.categories.generic'),
    images: [
      '/home-page-carousel/generic-PO.webp',
      '/home-page-carousel/generic-RFP.jpg',
      '/home-page-carousel/generic-invoice.png'
    ]
  },
  {
    id: 'handwritten',
    name: 'handwriten',
    displayName: t('carousel.categories.handwritten'),
    images: [
      '/home-page-carousel/handwriten-flowchart.webp',
      '/home-page-carousel/handwriten-form.png'
    ]
  },
  {
    id: 'insurance',
    name: 'insurance',
    displayName: t('carousel.categories.insurance'),
    images: [
      '/home-page-carousel/insurance-claimform.png',
      '/home-page-carousel/insurance.pnb.png'
    ]
  },
  {
    id: 'manufacturing',
    name: 'manufacturing',
    displayName: t('carousel.categories.manufacturing'),
    images: [
      '/home-page-carousel/manufacturing-spec.png'
    ]
  }
]

export function DocumentCarousel() {
  const { t } = useTranslation('homepage')
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [selectedCategory, setSelectedCategory] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  // Get document categories with translations
  const documentCategories = getDocumentCategories(t)

  // Get all images in order
  const allImages = documentCategories.flatMap(category => category.images)
  
  // Auto-rotate carousel
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isAnimating) {
        nextImage()
      }
    }, 4000)

    return () => clearInterval(interval)
  }, [currentImageIndex, isAnimating])

  const nextImage = () => {
    if (isAnimating) return
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentImageIndex((prev) => (prev + 1) % allImages.length)
      setIsAnimating(false)
    }, 600)
  }

  const prevImage = () => {
    if (isAnimating) return
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentImageIndex((prev) => (prev - 1 + allImages.length) % allImages.length)
      setIsAnimating(false)
    }, 600)
  }

  const goToCategory = (categoryIndex: number) => {
    if (isAnimating || categoryIndex === selectedCategory) return

    setIsAnimating(true)
    setSelectedCategory(categoryIndex)

    // Calculate the first image index for this category
    let imageIndex = 0
    for (let i = 0; i < categoryIndex; i++) {
      imageIndex += documentCategories[i].images.length
    }

    setTimeout(() => {
      setCurrentImageIndex(imageIndex)
      setIsAnimating(false)
    }, 600)
  }

  // Update selected category based on current image
  useEffect(() => {
    let imageCount = 0
    for (let i = 0; i < documentCategories.length; i++) {
      imageCount += documentCategories[i].images.length
      if (currentImageIndex < imageCount) {
        setSelectedCategory(i)
        break
      }
    }
  }, [currentImageIndex])

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            {t('carousel.title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {t('carousel.description')}
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8 lg:gap-12 items-start">
          {/* Left Side - Document Categories in 3x2 Grid */}
          <div className="w-full lg:w-1/3 order-2 lg:order-1">
            <h3 className="text-xl font-semibold text-gray-800 mb-6 text-center lg:text-left">{t('carousel.selectType')}</h3>
            <div className="grid grid-cols-2 gap-3">
              {documentCategories.map((category, categoryIndex) => (
                <div key={category.id} className="space-y-2">
                  {/* Category Header */}
                  <div className="font-medium text-sm text-gray-700 border-b pb-1">
                    {category.displayName}
                  </div>

                  {/* Document Files */}
                  <div className="space-y-2">
                    {category.images.map((image, imageIndex) => {
                      // Calculate the global index for this image
                      let globalIndex = 0;
                      for (let i = 0; i < categoryIndex; i++) {
                        globalIndex += documentCategories[i].images.length;
                      }
                      globalIndex += imageIndex;

                      // Extract file name from path and format it
                      const fileName = image.split('/').pop()?.split('.')[0] || '';
                      let formattedName = '';

                      // Custom mapping for specific file names
                      const fileNameMap: { [key: string]: string } = {
                        'contract-NDA': 'NDA',
                        'contract-employee': 'Employee Contract',
                        'financial-report': 'Financial Report',
                        'financial-statement': 'Financial Statement',
                        'generic-PO': 'Purchase Order',
                        'generic-RFP': 'RFP',
                        'generic-invoice': 'Invoice',
                        'handwriten-flowchart': 'Flowchart',
                        'handwriten-form': 'Form',
                        'insurance-claimform': 'Claim Form',
                        'insurance.pnb': 'Policy',
                        'manufacturing-spec': 'Specification'
                      };

                      formattedName = fileNameMap[fileName] || fileName.split('-').slice(1).join(' ').replace(/([A-Z])/g, ' $1').trim();

                      return (
                        <button
                          key={`${category.id}-${imageIndex}`}
                          onClick={() => {
                            if (!isAnimating) {
                              setIsAnimating(true);
                              setTimeout(() => {
                                setCurrentImageIndex(globalIndex);
                                setIsAnimating(false);
                              }, 600);
                            }
                          }}
                          disabled={isAnimating}
                          className={`w-full text-left p-2 rounded-lg border text-xs transition-all duration-300 ${
                            currentImageIndex === globalIndex
                              ? 'border-blue-400 bg-blue-50 text-blue-800 font-medium'
                              : 'border-gray-200 bg-white text-gray-600 hover:border-blue-300 hover:bg-blue-25'
                          } ${isAnimating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                        >
                          {formattedName || `Sample ${imageIndex + 1}`}
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Side - 3D Rolling Carousel */}
          <div className="w-full lg:w-2/3 order-1 lg:order-2 flex justify-center">
            <div className="relative">
              {/* 3D Carousel Container */}
              <div className="relative h-96 sm:h-[450px] lg:h-[600px] perspective-1000 overflow-hidden carousel-container">
                <div className="relative w-full h-full flex items-center justify-center">
                  {/* 3D Carousel Track */}
                  <div className="relative w-80 h-80 sm:w-96 sm:h-96 lg:w-[450px] lg:h-[450px] preserve-3d">
                    {allImages.map((image, index) => {
                      // Calculate position in 3D cylindrical carousel
                      const totalImages = allImages.length;
                      const angleStep = 360 / totalImages;
                      const angle = index * angleStep;
                      const radius = 280; // Distance from center

                      // Calculate rotation based on current image
                      const rotationY = angle - (currentImageIndex * angleStep);
                      const isActive = index === currentImageIndex;

                      return (
                        <div
                          key={index}
                          className={`absolute inset-0 transition-all duration-700 ease-in-out transform-gpu carousel-item ${
                            isAnimating ? 'transition-duration-700' : 'transition-duration-500'
                          } ${isActive ? 'carousel-item-active' : ''}`}
                          style={{
                            transform: `rotateY(${rotationY}deg) translateZ(${radius}px)`,
                            transformOrigin: 'center center',
                            zIndex: isActive ? 30 : 20 - Math.abs(index - currentImageIndex),
                            opacity: isActive ? 1 : Math.max(0.4, 1 - Math.abs(rotationY) / 180),
                          }}
                        >
                          <div
                            className={`w-full h-full bg-white rounded-xl lg:rounded-2xl shadow-2xl overflow-hidden p-4 transition-all duration-500 ${
                              isActive ? 'scale-110' : 'scale-90'
                            }`}
                          >
                            <img
                              src={image}
                              alt={`Document sample ${index + 1}`}
                              className="w-full h-full object-cover rounded-lg"
                            />
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>

              {/* Navigation Arrows */}
              <Button
                onClick={prevImage}
                disabled={isAnimating}
                variant="outline"
                size="icon"
                className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white border-gray-300 shadow-lg z-40 w-8 h-8 sm:w-10 sm:h-10 rounded-full"
                aria-label="Previous image"
              >
                <ChevronLeft className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>

              <Button
                onClick={nextImage}
                disabled={isAnimating}
                variant="outline"
                size="icon"
                className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white border-gray-300 shadow-lg z-40 w-8 h-8 sm:w-10 sm:h-10 rounded-full"
                aria-label="Next image"
              >
                <ChevronRight className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>

              {/* Dots Indicator */}
              <div className="flex justify-center mt-6 space-x-2">
                {allImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      if (!isAnimating) {
                        setIsAnimating(true)
                        setTimeout(() => {
                          setCurrentImageIndex(index)
                          setIsAnimating(false)
                        }, 600)
                      }
                    }}
                    disabled={isAnimating}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      currentImageIndex === index
                        ? 'bg-orange-500 scale-125'
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
