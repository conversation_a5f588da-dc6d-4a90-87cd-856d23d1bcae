'use client'

import React, { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useTranslation } from '@/hooks/use-translation'

interface DocumentCategory {
  id: string
  name: string
  displayName: string
  images: string[]
}

const getDocumentCategories = (t: any): DocumentCategory[] => [
  {
    id: 'contract',
    name: 'contract',
    displayName: t('carousel.categories.contract'),
    images: [
      '/home-page-carousel/contract-NDA.png',
      '/home-page-carousel/contract-employee.webp'
    ]
  },
  {
    id: 'financial',
    name: 'financial',
    displayName: t('carousel.categories.financial'),
    images: [
      '/home-page-carousel/financial-report.png',
      '/home-page-carousel/financial-statement.png'
    ]
  },
  {
    id: 'generic',
    name: 'generic',
    displayName: t('carousel.categories.generic'),
    images: [
      '/home-page-carousel/generic-PO.webp',
      '/home-page-carousel/generic-RFP.jpg',
      '/home-page-carousel/generic-invoice.png'
    ]
  },
  {
    id: 'handwritten',
    name: 'handwriten',
    displayName: t('carousel.categories.handwritten'),
    images: [
      '/home-page-carousel/handwriten-flowchart.webp',
      '/home-page-carousel/handwriten-form.png'
    ]
  },
  {
    id: 'insurance',
    name: 'insurance',
    displayName: t('carousel.categories.insurance'),
    images: [
      '/home-page-carousel/insurance-claimform.png',
      '/home-page-carousel/insurance.pnb.png'
    ]
  },
  {
    id: 'manufacturing',
    name: 'manufacturing',
    displayName: t('carousel.categories.manufacturing'),
    images: [
      '/home-page-carousel/manufacturing-spec.png'
    ]
  }
]

export function DocumentCarousel() {
  const { t } = useTranslation('homepage')
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [selectedCategory, setSelectedCategory] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  // Get document categories with translations
  const documentCategories = getDocumentCategories(t)

  // Get all images in order
  const allImages = documentCategories.flatMap(category => category.images)
  
  // Auto-rotate carousel
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isAnimating) {
        nextImage()
      }
    }, 4000)

    return () => clearInterval(interval)
  }, [currentImageIndex, isAnimating])

  const nextImage = () => {
    if (isAnimating) return
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentImageIndex((prev) => (prev + 1) % allImages.length)
      setIsAnimating(false)
    }, 600)
  }

  const prevImage = () => {
    if (isAnimating) return
    setIsAnimating(true)
    setTimeout(() => {
      setCurrentImageIndex((prev) => (prev - 1 + allImages.length) % allImages.length)
      setIsAnimating(false)
    }, 600)
  }

  const goToCategory = (categoryIndex: number) => {
    if (isAnimating || categoryIndex === selectedCategory) return

    setIsAnimating(true)
    setSelectedCategory(categoryIndex)

    // Calculate the first image index for this category
    let imageIndex = 0
    for (let i = 0; i < categoryIndex; i++) {
      imageIndex += documentCategories[i].images.length
    }

    setTimeout(() => {
      setCurrentImageIndex(imageIndex)
      setIsAnimating(false)
    }, 600)
  }

  // Update selected category based on current image
  useEffect(() => {
    let imageCount = 0
    for (let i = 0; i < documentCategories.length; i++) {
      imageCount += documentCategories[i].images.length
      if (currentImageIndex < imageCount) {
        setSelectedCategory(i)
        break
      }
    }
  }, [currentImageIndex])

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            {t('carousel.title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {t('carousel.description')}
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8 lg:gap-12 items-start">
          {/* Left Side - Document List */}
          <div className="w-full lg:w-1/3 order-2 lg:order-1">
            <h3 className="text-xl font-semibold text-gray-800 mb-6 text-center lg:text-left">{t('carousel.selectType')}</h3>
            <div className="space-y-2">
              {allImages.map((image, index) => {
                // Find which category this image belongs to
                let categoryName = ''
                let imageIndexInCategory = 0
                let currentIndex = 0

                for (const category of documentCategories) {
                  if (currentIndex + category.images.length > index) {
                    categoryName = category.displayName
                    imageIndexInCategory = index - currentIndex
                    break
                  }
                  currentIndex += category.images.length
                }

                return (
                  <button
                    key={index}
                    onClick={() => {
                      if (!isAnimating) {
                        setIsAnimating(true)
                        setTimeout(() => {
                          setCurrentImageIndex(index)
                          setIsAnimating(false)
                        }, 600)
                      }
                    }}
                    disabled={isAnimating}
                    className={`w-full text-left p-3 rounded-lg border transition-all duration-300 ${
                      currentImageIndex === index
                        ? 'border-blue-400 bg-blue-50 text-blue-800'
                        : 'border-gray-200 bg-white text-gray-700 hover:border-blue-300 hover:bg-blue-25'
                    } ${isAnimating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                  >
                    <div className="font-medium text-sm">
                      {categoryName}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Sample {imageIndexInCategory + 1}
                    </div>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Right Side - 3D Rolling Carousel */}
          <div className="w-full lg:w-2/3 order-1 lg:order-2 flex justify-center">
            <div className="relative">
              {/* Carousel Container */}
              <div className="relative h-96 sm:h-[450px] lg:h-[600px] perspective-1000 overflow-hidden">
                <div className="relative w-full h-full flex items-center justify-center">
                  {/* Rolling Carousel Track */}
                  <div className="relative w-80 h-80 sm:w-96 sm:h-96 lg:w-[450px] lg:h-[450px] preserve-3d">
                    {allImages.map((image, index) => {
                      const offset = index - currentImageIndex
                      const isVisible = Math.abs(offset) <= 3

                      if (!isVisible) return null

                      // Calculate rotation angle for cylindrical effect
                      const rotationAngle = offset * 60 // 60 degrees between each image
                      const radius = 250 // Distance from center

                      return (
                        <div
                          key={index}
                          className={`absolute inset-0 transition-all duration-700 ease-in-out transform-gpu carousel-item ${
                            isAnimating ? 'transition-duration-700' : 'transition-duration-500'
                          }`}
                          style={{
                            transform: `
                              rotateY(${rotationAngle}deg)
                              translateZ(${offset === 0 ? radius : radius * 0.8}px)
                              scale(${offset === 0 ? 1 : 0.85})
                            `,
                            opacity: offset === 0 ? 1 : Math.max(0.3, 1 - Math.abs(offset) * 0.3),
                            zIndex: offset === 0 ? 10 : 10 - Math.abs(offset)
                          }}
                        >
                          <div className="w-full h-full bg-white rounded-xl lg:rounded-2xl shadow-2xl overflow-hidden p-4">
                            <img
                              src={image}
                              alt={`Document sample ${index + 1}`}
                              className="w-full h-full object-cover rounded-lg"
                            />
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>

              {/* Navigation Arrows */}
              <Button
                onClick={prevImage}
                disabled={isAnimating}
                variant="outline"
                size="icon"
                className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white border-gray-300 shadow-lg z-10 w-8 h-8 sm:w-10 sm:h-10"
              >
                <ChevronLeft className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>

              <Button
                onClick={nextImage}
                disabled={isAnimating}
                variant="outline"
                size="icon"
                className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 bg-white/90 hover:bg-white border-gray-300 shadow-lg z-10 w-8 h-8 sm:w-10 sm:h-10"
              >
                <ChevronRight className="h-4 w-4 sm:h-5 sm:w-5" />
              </Button>

              {/* Dots Indicator */}
              <div className="flex justify-center mt-6 space-x-2">
                {allImages.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      if (!isAnimating) {
                        setIsAnimating(true)
                        setTimeout(() => {
                          setCurrentImageIndex(index)
                          setIsAnimating(false)
                        }, 600)
                      }
                    }}
                    disabled={isAnimating}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      currentImageIndex === index
                        ? 'bg-orange-500 scale-125'
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
