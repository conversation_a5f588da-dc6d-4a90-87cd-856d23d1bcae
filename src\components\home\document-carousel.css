/* 3D Carousel Styles */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.transform-style-3d {
  transform-style: preserve-3d;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

@keyframes spin-slow {
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(360deg);
  }
}

/* Pause animation on hover */
.carousel-container:hover .animate-spin-slow {
  animation-play-state: paused;
}

/* Enhanced 3D effects */
.carousel-item {
  backface-visibility: hidden;
  will-change: transform;
}

/* Smooth transitions for 3D transforms */
.carousel-3d {
  transition: transform 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* Active item glow effect */
.carousel-item-active {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}
