{"name": "web-template", "version": "0.1.0", "private": true, "scripts": {"dev": "concurrently \"next dev --turbopack\" \"npm run stripe:listen\"", "dev:next": "next dev --turbopack", "dev:prod": "cp .env.prod .env && concurrently \"next dev --turbopack\" \"npm run stripe:listen\"", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "setup:stripe": "node scripts/setup-stripe.js", "create:stripe": "node scripts/create-stripe-products.js", "validate-config": "node scripts/validate-config.js", "postinstall": "prisma generate", "stripe:listen": "stripe\\stripe.exe listen --forward-to localhost:3000/api/webhooks/stripe"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.10.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@stripe/stripe-js": "^7.4.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.0.0", "embla-carousel-react": "^8.6.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mysql2": "^3.14.1", "next": "15.3.4", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-markdown": "^10.1.0", "recharts": "^3.0.2", "remark-gfm": "^4.0.1", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.1", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^9.2.0", "eslint": "^9", "eslint-config-next": "15.3.4", "jest-environment-jsdom": "^30.0.4", "tailwindcss": "^4", "ts-jest": "^29.4.0", "typescript": "^5"}}