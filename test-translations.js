// Simple test to verify translations are working
const fs = require('fs');
const path = require('path');

console.log('Testing Chinese translations...');

// Test homepage translations
try {
  const zhHomepage = JSON.parse(fs.readFileSync(path.join(__dirname, 'locales/zh/homepage.json'), 'utf8'));
  const enHomepage = JSON.parse(fs.readFileSync(path.join(__dirname, 'locales/en/homepage.json'), 'utf8'));
  
  console.log('✅ Homepage translations loaded successfully');
  
  // Test the new valueProposition translations
  if (zhHomepage.valueProposition && zhHomepage.valueProposition.heavyLifting) {
    console.log('✅ Chinese valueProposition.heavyLifting:', zhHomepage.valueProposition.heavyLifting);
  } else {
    console.log('❌ Missing Chinese valueProposition.heavyLifting');
  }
  
  if (zhHomepage.valueProposition && zhHomepage.valueProposition.startSaving) {
    console.log('✅ Chinese valueProposition.startSaving:', zhHomepage.valueProposition.startSaving);
  } else {
    console.log('❌ Missing Chinese valueProposition.startSaving');
  }
  
  if (enHomepage.valueProposition && enHomepage.valueProposition.heavyLifting) {
    console.log('✅ English valueProposition.heavyLifting:', enHomepage.valueProposition.heavyLifting);
  } else {
    console.log('❌ Missing English valueProposition.heavyLifting');
  }
  
  if (enHomepage.valueProposition && enHomepage.valueProposition.startSaving) {
    console.log('✅ English valueProposition.startSaving:', enHomepage.valueProposition.startSaving);
  } else {
    console.log('❌ Missing English valueProposition.startSaving');
  }
  
} catch (error) {
  console.log('❌ Error loading homepage translations:', error.message);
}

// Test pricing translations
try {
  const zhPricing = JSON.parse(fs.readFileSync(path.join(__dirname, 'locales/zh/pricing.json'), 'utf8'));
  console.log('✅ Chinese pricing translations loaded successfully');
  console.log('✅ Chinese mostPopular:', zhPricing.mostPopular);
} catch (error) {
  console.log('❌ Error loading pricing translations:', error.message);
}

console.log('\nTranslation test completed!');
